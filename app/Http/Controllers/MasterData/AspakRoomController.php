<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Models\AspakServiceRoom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class AspakRoomController extends Controller
{
    public function index()
    {
        $title = "Ruangan";
        $breadcrumbs = ["Master Data", "Aspak", "Ruangan"];

        return view('master-data.aspak.room.index', compact('title', 'breadcrumbs'));
    }

    public function list(Request $request)
    {
        if (!$request->ajax()) {
            return $this->emptyDataTablesResponse();
        }

        $params = $this->extractDataTablesParams($request);
        $searchQuery = $params['search'];

        // Build base query with optimized eager loading
        $query = $this->buildBaseQuery($searchQuery);

        // Get total records count before pagination
        $totalRecords = $this->getTotalRecords($searchQuery);

        // Apply pagination
        $paginatedData = $query->offset($params['start'])
            ->limit($params['length'])
            ->get();

        // Build hierarchical data efficiently
        $hierarchicalData = $this->buildHierarchicalData($paginatedData);

        return response()->json([
            'data' => $hierarchicalData,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $totalRecords
        ]);
    }

    /**
     * Return empty DataTables response
     */
    private function emptyDataTablesResponse()
    {
        return response()->json([
            'data' => [],
            'recordsTotal' => 0,
            'recordsFiltered' => 0
        ]);
    }

    /**
     * Extract DataTables parameters from request
     */
    private function extractDataTablesParams(Request $request)
    {
        return [
            'start' => (int) $request->get('start', 0),
            'length' => (int) $request->get('length', 10),
            'search' => $request->get('search')['value'] ?? '',
            'order_column' => $request->get('order')[0]['column'] ?? 0,
            'order_dir' => $request->get('order')[0]['dir'] ?? 'asc'
        ];
    }

    /**
     * Build base query with optimized eager loading
     */
    private function buildBaseQuery($searchQuery = null)
    {
        $query = AspakServiceRoom::select([
            'id', 'parent_id', 'room_service_name',
            'room_service_code', 'type'
        ])
        ->with([
            'parent:id,room_service_name,parent_id',
            'children:id,parent_id,room_service_name,room_service_code,type'
        ])
        ->orderBy('parent_id', 'asc')
        ->orderBy('room_service_name', 'asc');

        if ($searchQuery) {
            $query->where(function ($q) use ($searchQuery) {
                $q->where('room_service_name', 'like', "%{$searchQuery}%")
                  ->orWhere('room_service_code', 'like', "%{$searchQuery}%");
            });
        }

        return $query;
    }

    /**
     * Get total records count with caching
     */
    private function getTotalRecords($searchQuery = null)
    {
        // Use cache for total count when no search query
        if (empty($searchQuery)) {
            return Cache::remember('aspak_rooms_total_count', 300, function () {
                return AspakServiceRoom::count();
            });
        }

        // Don't cache search results as they vary
        $query = AspakServiceRoom::query();
        $query->where(function ($q) use ($searchQuery) {
            $q->where('room_service_name', 'like', "%{$searchQuery}%")
              ->orWhere('room_service_code', 'like', "%{$searchQuery}%");
        });

        return $query->count();
    }

    /**
     * Build hierarchical data efficiently
     */
    private function buildHierarchicalData($data)
    {
        $result = [];
        $index = 0;

        foreach ($data as $item) {
            $index++;
            $result[] = $this->formatDataTableRow($item, $index);
        }

        return $result;
    }

    /**
     * Format single row for DataTables
     */
    private function formatDataTableRow(AspakServiceRoom $item, $index, $level = 0)
    {
        return [
            'DT_RowIndex' => $index,
            'id' => $item->id,
            'parent_id' => $item->parent_id,
            'parent_name' => $item->parent ? $item->parent->room_service_name : '-',
            'room_service_name' => $item->room_service_name,
            'room_service_code' => $item->room_service_code,
            'type' => $item->type,
            'level' => $level,
            'has_children' => $item->children->count() > 0,
            'is_expanded' => true
        ];
    }

    public function edit(AspakServiceRoom $aspakServiceRoom)
    {
        if (!hasPermissionInGuard('Aspak Room - View')) {
            abort(403, "Unauthorized action.");
        }

        try {
            // Load parent relationship jika ada
            $aspakServiceRoom->load('parent');

            return response()->json([
                "data" => $aspakServiceRoom
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => "Data tidak ditemukan"
            ], 404);
        }
    }

    public function destroy(AspakServiceRoom $aspakServiceRoom)
    {
        if (!hasPermissionInGuard('Aspak Room - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            // Cek apakah item memiliki children
            $hasChildren = AspakServiceRoom::where('parent_id', $aspakServiceRoom->id)->exists();

            if ($hasChildren) {
                return response()->json([
                    "message" => "Data tidak dapat dihapus karena masih memiliki data anak"
                ], 422);
            }

            // Set deleted_by sebelum soft delete
            $aspakServiceRoom->deleted_by = getAuthUserId();
            $aspakServiceRoom->save();

            // Soft delete
            $aspakServiceRoom->delete();

            // Clear cache after deleting record
            $this->clearAspakRoomCache();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
    {
        if (!hasPermissionInGuard('Aspak Room - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'room_service_code' => 'required|string|unique:aspak_service_rooms,room_service_code',
            'room_service_name' => 'required|string',
            'type' => 'required|in:GROUP,ROOM_SERVICE',
            'parent_id' => 'nullable|exists:aspak_service_rooms,id'
        ]);

        try {
            DB::beginTransaction();

            // Validasi parent_id harus GROUP jika diisi
            if ($request->parent_id) {
                $parent = AspakServiceRoom::find($request->parent_id);
                if (!$parent || $parent->type !== 'GROUP') {
                    return response()->json([
                        "message" => "Parent harus bertipe GROUP"
                    ], 422);
                }
            }

            AspakServiceRoom::create([
                'room_service_code' => $request->room_service_code,
                'room_service_name' => $request->room_service_name,
                'type' => $request->type,
                'parent_id' => $request->parent_id,
                'created_by' => getAuthUserId()
            ]);

            // Clear cache after creating new record
            $this->clearAspakRoomCache();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function create(Request $request)
    {
        if (!hasPermissionInGuard('Aspak Room - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            // Get available parents (only GROUP type)
            $parents = AspakServiceRoom::where('type', 'GROUP')
                ->orderBy('room_service_name', 'ASC')
                ->get(['id', 'room_service_name', 'parent_id']);

            $data = [
                'parents' => $parents,
                'parent_id' => $request->get('parent_id', null)
            ];

            return response()->json([
                "data" => $data
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, AspakServiceRoom $aspakServiceRoom)
    {
        if (!hasPermissionInGuard('Aspak Room - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'room_service_code' => 'required|string|unique:aspak_service_rooms,room_service_code,' . $aspakServiceRoom->id,
            'room_service_name' => 'required|string',
            'type' => 'required|in:GROUP,ROOM_SERVICE',
            'parent_id' => 'nullable|exists:aspak_service_rooms,id'
        ]);

        try {
            DB::beginTransaction();

            // Validasi parent_id harus GROUP jika diisi
            if ($request->parent_id) {
                $parent = AspakServiceRoom::find($request->parent_id);
                if (!$parent || $parent->type !== 'GROUP') {
                    return response()->json([
                        "message" => "Parent harus bertipe GROUP"
                    ], 422);
                }

                // Validasi tidak boleh memilih dirinya sendiri atau anaknya sebagai parent
                if ($request->parent_id == $aspakServiceRoom->id) {
                    return response()->json([
                        "message" => "Tidak dapat memilih diri sendiri sebagai parent"
                    ], 422);
                }

                // Cek apakah parent_id adalah anak dari item ini
                $isChild = $this->isChildOf($request->parent_id, $aspakServiceRoom->id);
                if ($isChild) {
                    return response()->json([
                        "message" => "Tidak dapat memilih anak sebagai parent"
                    ], 422);
                }
            }

            $aspakServiceRoom->update([
                'room_service_code' => $request->room_service_code,
                'room_service_name' => $request->room_service_name,
                'type' => $request->type,
                'parent_id' => $request->parent_id,
                'updated_by' => getAuthUserId()
            ]);

            // Clear cache after updating record
            $this->clearAspakRoomCache();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    /**
     * Clear AspakRoom related cache
     */
    private function clearAspakRoomCache()
    {
        Cache::forget('aspak_rooms_total_count');
        // Clear other related cache keys if needed
        Cache::tags(['aspak_rooms'])->flush();
    }

    /**
     * Check if parentId is a child of itemId
     */
    private function isChildOf($parentId, $itemId)
    {
        $parent = AspakServiceRoom::find($parentId);
        if (!$parent) {
            return false;
        }

        if ($parent->parent_id == $itemId) {
            return true;
        }

        if ($parent->parent_id) {
            return $this->isChildOf($parent->parent_id, $itemId);
        }

        return false;
    }
}
