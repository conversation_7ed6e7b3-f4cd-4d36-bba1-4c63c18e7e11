$(document).ready(function () {
    // Initialize DataTable
    var table = $('#table-data').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '/master-data/aspak/room/list',
            type: 'GET'
        },
        columns: [
            {
                data: 'DT_RowIndex',
                name: 'DT_RowIndex',
                orderable: false,
                searchable: false,
                width: '5%',
                className: 'text-center'
            },
            {
                data: 'room_service_name',
                name: 'room_service_name',
                render: function(data, type, row) {
                    return renderTreeCell(data, row);
                },
                width: '40%'
            },
            {
                data: 'room_service_code',
                name: 'room_service_code',
                width: '15%'
            },
            {
                data: 'parent_name',
                name: 'parent_name',
                orderable: false,
                searchable: false,
                width: '15%',
                className: 'text-center'
            },
            {
                data: 'type',
                name: 'type',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return renderTypeBadge(data);
                },
                width: '15%',
                className: 'text-center'
            },
            {
                data: null,
                name: 'action',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return renderActionButtons(row);
                },
                width: '20%',
                className: 'text-center'
            }
        ],
        order: [[1, 'asc']],
        paging: false,
        dom: 'Bfrtip',
        language: {
            processing: "Sedang memproses...",
            lengthMenu: "Tampilkan _MENU_ entri",
            zeroRecords: "Tidak ditemukan data yang sesuai",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 entri",
            infoFiltered: "(disaring dari _MAX_ entri keseluruhan)",
            search: "Cari:",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        // Add tree grid classes and attributes to rows
        createdRow: function(row, data, dataIndex) {
            setupTreeRow($(row), data);
        },
        // Custom search to handle hierarchy
        search: {
            regex: false,
            smart: true
        }
    });

    // Function to render tree cell with hierarchy
    function renderTreeCell(data, row) {
        var level = row.level || 0;
        var hasChildren = row.has_children || false;
        var type = row.type || 'ROOM_SERVICE';
        var id = row.id || 0;
        var isExpanded = row.is_expanded !== false; // Default expanded

        // Create indentation for the entire cell
        var indent = '';
        for (var i = 0; i < level; i++) {
            indent += '<span class="tree-indent tree-indent-level-' + i + '"></span>';
        }

        // Expand/collapse toggle with proper indentation
        var toggle = '';
        if (hasChildren && type === 'GROUP') {
            var toggleClass = isExpanded ? 'fa-chevron-down' : 'fa-chevron-right';
            toggle = '<i class="fas ' + toggleClass + ' tree-toggle tree-toggle-level-' + level + '" data-id="' + id + '" data-expanded="' + isExpanded + '"></i>';
        } else {
            toggle = '<span class="tree-toggle-placeholder tree-toggle-level-' + level + '"></span>';
        }

        // Icon based on type
        var iconClass = type === 'GROUP' ? 'fa-folder tree-icon folder' : 'fa-file tree-icon file';
        var icon = '<i class="fas ' + iconClass + '"></i>';

        return '<div class="tree-cell">' + indent + toggle + icon + '<span class="tree-name">' + (data || '') + '</span></div>';
    }

    // Function to render type badge
    function renderTypeBadge(type) {
        var badgeClass = type === 'GROUP' ? 'type-badge group' : 'type-badge room-service';
        var badgeText = type === 'GROUP' ? 'GROUP' : 'ROOM SERVICE';
        return '<span class="' + badgeClass + '">' + badgeText + '</span>';
    }

    // Function to render action buttons
    function renderActionButtons(row) {
        var id = row.id || 0;
        var editBtn = '<button class="btn btn-sm btn-primary me-1 btn-edit" data-id="' + id + '" title="Edit">' +
                     '<i class="fas fa-edit"></i></button>';
        var deleteBtn = '<button class="btn btn-sm btn-danger btn-delete" data-id="' + id + '" title="Delete">' +
                       '<i class="fas fa-trash"></i></button>';
        return '<div class="action-buttons">' + editBtn + deleteBtn + '</div>';
    }

    // Function to setup tree row attributes and classes
    function setupTreeRow(row, data) {
        if (!data) return;

        var level = data.level || 0;
        var parentId = data.parent_id || null;
        var type = data.type || 'ROOM_SERVICE';
        var id = data.id || 0;
        var isExpanded = data.is_expanded !== false;

        // Add data attributes
        row.attr('data-id', id);
        row.attr('data-level', level);
        row.attr('data-type', type);
        row.attr('data-expanded', isExpanded);

        if (parentId) {
            row.attr('data-parent-id', parentId);
        }

        // Add CSS classes
        row.addClass('tree-row');
        row.addClass('tree-level-' + level);

        // Add level-specific background and styling classes
        row.addClass('tree-row-level-' + level);

        if (type === 'GROUP') {
            row.addClass('tree-group');
        } else {
            row.addClass('tree-item');
        }

        // Store row data
        row.data('tree-data', data);
    }

    // Tree grid expand/collapse functionality
    $(document).on('click', '.tree-toggle', function(e) {
        e.stopPropagation();

        var toggle = $(this);
        var row = toggle.closest('tr');
        var id = toggle.data('id');
        var isExpanded = toggle.data('expanded');
        var level = parseInt(row.attr('data-level')) || 0;

        if (isExpanded) {
            // Collapse: hide all descendants
            collapseNode(id, level);
            toggle.removeClass('fa-chevron-down').addClass('fa-chevron-right');
            toggle.data('expanded', false);
            row.attr('data-expanded', false);
        } else {
            // Expand: show direct children
            expandNode(id, level);
            toggle.removeClass('fa-chevron-right').addClass('fa-chevron-down');
            toggle.data('expanded', true);
            row.attr('data-expanded', true);
        }
    });

    // Function to collapse a node and all its descendants
    function collapseNode(parentId, parentLevel) {
        var childLevel = parentLevel + 1;

        // Find and hide all descendants
        table.rows().every(function() {
            var row = $(this.node());
            var rowLevel = parseInt(row.attr('data-level')) || 0;
            var rowParentId = row.attr('data-parent-id');

            // Hide if it's a descendant (higher level and has this node as ancestor)
            if (rowLevel > parentLevel && isDescendantOf(row, parentId)) {
                row.hide();
                // Also collapse any expanded descendants
                var toggle = row.find('.tree-toggle');
                if (toggle.length && toggle.data('expanded')) {
                    toggle.removeClass('fa-chevron-down').addClass('fa-chevron-right');
                    toggle.data('expanded', false);
                    row.attr('data-expanded', false);
                }
            }
        });
    }

    // Function to expand a node (show direct children only)
    function expandNode(parentId, parentLevel) {
        var childLevel = parentLevel + 1;

        // Show direct children only
        table.rows().every(function() {
            var row = $(this.node());
            var rowLevel = parseInt(row.attr('data-level')) || 0;
            var rowParentId = row.attr('data-parent-id');

            // Show if it's a direct child
            if (rowLevel === childLevel && rowParentId == parentId) {
                row.show();
            }
        });
    }

    // Function to check if a row is descendant of a parent
    function isDescendantOf(row, ancestorId) {
        var currentParentId = row.attr('data-parent-id');

        while (currentParentId) {
            if (currentParentId == ancestorId) {
                return true;
            }

            // Find parent row to get its parent
            var parentRow = null;
            table.rows().every(function() {
                var r = $(this.node());
                if (r.attr('data-id') == currentParentId) {
                    parentRow = r;
                    return false; // break
                }
            });

            if (parentRow) {
                currentParentId = parentRow.attr('data-parent-id');
            } else {
                break;
            }
        }

        return false;
    }

    // Initialize tree state after data is loaded
    table.on('draw', function() {
        initializeTreeState();
    });

    // Function to initialize tree state (default expanded all)
    function initializeTreeState() {
        // Show all rows initially (default expanded all)
        table.rows().every(function() {
            var row = $(this.node());
            row.show();
        });
    }

    // Enhanced search functionality for tree grid
    $('#searchInput').on('keyup', function () {
        var searchTerm = this.value;

        if (searchTerm) {
            // When searching, show matching rows and their ancestors
            table.search(searchTerm).draw();

            // After search, ensure parent rows of matches are visible
            setTimeout(function() {
                showParentsOfVisibleRows();
            }, 100);
        } else {
            // Clear search and restore tree state
            table.search('').draw();
            setTimeout(function() {
                initializeTreeState();
            }, 100);
        }
    });

    // Function to show parent rows of visible matches
    function showParentsOfVisibleRows() {
        table.rows({ search: 'applied' }).every(function() {
            var row = $(this.node());
            showParentChain(row);
        });
    }

    // Function to show parent chain for a row
    function showParentChain(row) {
        var parentId = row.attr('data-parent-id');

        while (parentId) {
            var parentRow = null;
            table.rows().every(function() {
                var r = $(this.node());
                if (r.attr('data-id') == parentId) {
                    parentRow = r;
                    return false; // break
                }
            });

            if (parentRow) {
                parentRow.show();
                // Expand parent if it has toggle
                var toggle = parentRow.find('.tree-toggle');
                if (toggle.length && !toggle.data('expanded')) {
                    toggle.removeClass('fa-chevron-right').addClass('fa-chevron-down');
                    toggle.data('expanded', true);
                    parentRow.attr('data-expanded', true);
                }
                parentId = parentRow.attr('data-parent-id');
            } else {
                break;
            }
        }
    }

    // Reload table function
    window.reloadTable = function () {
        table.ajax.reload(function() {
            // Reinitialize tree state after reload
            setTimeout(function() {
                initializeTreeState();
            }, 100);
        }, false);
    };

    // Event handler untuk tombol tambah data
    $('#btn-add').on('click', function () {
        resetForm();
        $('#modal-form').modal('show');
        $('#formdata').attr('action', '/aspak/room');
        $('.modal-title').text('Tambah Aspak Room');
        loadParentOptions();
    });

    // Event handler untuk tombol edit
    $(document).on('click', '.btn-edit', function () {
        var id = $(this).data('id');
        resetForm();

        $.ajax({
            url: '/master-data/aspak/room/' + id + '/edit',
            type: 'GET',
            success: function (response) {
                if (response.success) {
                    var data = response.data;
                    $('#room_service_code').val(data.room_service_code);
                    $('#room_service_name').val(data.room_service_name);
                    $('#type').val(data.type);

                    loadParentOptions(data.parent_id);

                    $('#formdata').attr('action', '/master-data/aspak/room/' + id);
                    $('#formdata').append('<input type="hidden" name="_method" value="PUT">');
                    $('.modal-title').text('Edit Aspak Room');
                    $('#modal-form').modal('show');
                }
            },
            error: function (xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Gagal mengambil data'
                });
            }
        });
    });

    // Event handler untuk tombol hapus
    $(document).on('click', '.btn-delete', function () {
        var id = $(this).data('id');
        var name = $(this).data('name');

        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: 'Apakah Anda yakin ingin menghapus "' + name + '"?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '/master-data/aspak/room/' + id,
                    type: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil!',
                                text: response.message,
                                timer: 2000,
                                showConfirmButton: false
                            });
                            table.ajax.reload();
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: response.message
                            });
                        }
                    },
                    error: function (xhr) {
                        var response = xhr.responseJSON;
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: response ? response.message : 'Gagal menghapus data'
                        });
                    }
                });
            }
        });
    });

    // Event handler untuk tombol simpan
    $('#btn-save').on('click', function () {
        var form = $('#formdata');
        var formData = new FormData(form[0]);
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        // Clear previous errors
        $('.error-message').text('');
        $('.form-control').removeClass('is-invalid');

        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                if (response.success) {
                    $('#modal-form').modal('hide');
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: response.message,
                        timer: 2000,
                        showConfirmButton: false
                    });
                    table.ajax.reload();
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: response.message
                    });
                }
            },
            error: function (xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $.each(errors, function (key, value) {
                        $('#error_' + key).text(value[0]);
                        $('[name="' + key + '"]').addClass('is-invalid');
                    });
                } else {
                    var response = xhr.responseJSON;
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: response ? response.message : 'Terjadi kesalahan'
                    });
                }
            }
        });
    });

    // Event handler untuk tombol import excel
    $('#btn-import').on('click', function () {
        $('#modal-import-excel').modal('show');
        resetImportForm();
    });

    // Event handler untuk submit import excel
    $('#btn-import-submit').on('click', function () {
        var form = $('#form-import-excel');
        var formData = new FormData(form[0]);
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        // Clear previous messages
        $('#import-message-area').addClass('d-none');
        $('#import-success-message, #import-error-message').addClass('d-none');
        $('.error-message').text('');

        $.ajax({
            url: '/master-data/aspak/room/import',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                if (response.success) {
                    $('#success-text').text(response.message);
                    $('#import-success-message').removeClass('d-none');
                    $('#import-message-area').removeClass('d-none');
                    table.ajax.reload();

                    setTimeout(function () {
                        $('#modal-import-excel').modal('hide');
                    }, 2000);
                } else {
                    $('#error-text').text(response.message);
                    if (response.errors) {
                        var errorHtml = '<ul class="mb-0">';
                        $.each(response.errors, function (index, error) {
                            errorHtml += '<li>' + error + '</li>';
                        });
                        errorHtml += '</ul>';
                        $('#error-details').html(errorHtml);
                    }
                    $('#import-error-message').removeClass('d-none');
                    $('#import-message-area').removeClass('d-none');
                }
            },
            error: function (xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $.each(errors, function (key, value) {
                        $('#error_' + key).text(value[0]);
                    });
                } else {
                    var response = xhr.responseJSON;
                    $('#error-text').text(response ? response.message : 'Terjadi kesalahan saat import');
                    $('#import-error-message').removeClass('d-none');
                    $('#import-message-area').removeClass('d-none');
                }
            }
        });
    });

    // Function untuk reset form
    function resetForm() {
        $('#formdata')[0].reset();
        $('.error-message').text('');
        $('.form-control').removeClass('is-invalid');
        $('input[name="_method"]').remove();
    }

    // Function untuk reset import form
    function resetImportForm() {
        $('#form-import-excel')[0].reset();
        $('#import-message-area').addClass('d-none');
        $('#import-success-message, #import-error-message').addClass('d-none');
        $('.error-message').text('');
    }

    // Function untuk load parent options
    function loadParentOptions(selectedValue = null) {
        $.ajax({
            url: '/master-data/aspak/room/list',
            type: 'GET',
            data: {
                type: 'GROUP' // Hanya ambil yang tipe GROUP untuk parent
            },
            success: function (response) {
                var options = '<option value="">Pilih Parent (Kosong untuk Root)</option>';

                if (response.data && response.data.length > 0) {
                    $.each(response.data, function (index, item) {
                        if (item.type === 'GROUP') {
                            var selected = selectedValue == item.id ? 'selected' : '';
                            var indent = '&nbsp;'.repeat(item.level * 4);
                            options += '<option value="' + item.id + '" ' + selected + '>' + indent + item.room_service_name + '</option>';
                        }
                    });
                }

                $('#parent_id').html(options);
            },
            error: function (xhr) {
                console.log('Error loading parent options:', xhr);
            }
        });
    }
});
