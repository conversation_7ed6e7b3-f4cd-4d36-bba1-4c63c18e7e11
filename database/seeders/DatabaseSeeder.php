<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);

        // $this->call(UserSeeder::class);
        // $this->call(SatuanSeeder::class);
        // $this->call(AsalPerolehanSeeder::class);
        $this->call(RoleSeeder::class);
        $this->call(PermissionSeeder::class);
        $this->call(PermissionWebSeeder::class);
        $this->call(AccessRightSeeder::class);
        $this->call(AspakServiceRoomSeeder::class);
    }
}
