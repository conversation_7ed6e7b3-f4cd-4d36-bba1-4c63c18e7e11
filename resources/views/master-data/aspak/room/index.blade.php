@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
          rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>

    <link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet"/>

    <style>
        /* Tree Grid Styling */
        .tree-grid-container {
            position: relative;
        }

        .tree-row {
            position: relative;
        }

        .tree-cell {
            display: flex;
            align-items: center;
            padding: 4px 0; /* Reduced padding for compact UI */
        }

        /* Indentation for hierarchy levels - Compact spacing */
        .tree-indent {
            display: inline-block;
            width: 18px; /* Reduced from 24px */
            flex-shrink: 0;
        }

        .tree-indent-level-0 { margin-left: 0px; }
        .tree-indent-level-1 { margin-left: 14px; }
        .tree-indent-level-2 { margin-left: 28px; }
        .tree-indent-level-3 { margin-left: 42px; }
        .tree-indent-level-4 { margin-left: 56px; }
        .tree-indent-level-5 { margin-left: 70px; }

        /* Level-specific background colors with gradation and type differentiation */
        /* GROUP (Folder) - Blue-Green palette */
        .tree-row-level-0.tree-row-group {
            background-color: #f0f9ff !important; /* Very light blue */
            font-size: 14px;
            font-weight: 600;
        }

        .tree-row-level-1.tree-row-group {
            background-color: #e0f2fe !important; /* Light cyan */
            font-size: 13px;
            font-weight: 500;
        }

        .tree-row-level-2.tree-row-group {
            background-color: #b3e5fc !important; /* Light blue */
            font-size: 12px;
            font-weight: 400;
        }

        .tree-row-level-3.tree-row-group {
            background-color: #81d4fa !important; /* Medium light blue */
            font-size: 11px;
            font-weight: 400;
        }

        .tree-row-level-4.tree-row-group {
            background-color: #4fc3f7 !important; /* Medium blue */
            font-size: 10px;
            font-weight: 400;
            color: #1a1a1a !important; /* Darker text for better contrast */
        }

        .tree-row-level-5.tree-row-group {
            background-color: #29b6f6 !important; /* Deeper blue */
            font-size: 10px;
            font-weight: 400;
            color: #ffffff !important; /* White text for better contrast on darker background */
        }

        /* ROOM_SERVICE (File) - Purple-Pink palette */
        .tree-row-level-0.tree-row-room-service {
            background-color: #fdf2f8 !important; /* Very light pink */
            font-size: 14px;
            font-weight: 600;
        }

        .tree-row-level-1.tree-row-room-service {
            background-color: #fce7f3 !important; /* Light pink */
            font-size: 13px;
            font-weight: 500;
        }

        .tree-row-level-2.tree-row-room-service {
            background-color: #f9d5e5 !important; /* Soft pink */
            font-size: 12px;
            font-weight: 400;
        }

        .tree-row-level-3.tree-row-room-service {
            background-color: #f3b4d1 !important; /* Medium pink */
            font-size: 11px;
            font-weight: 400;
        }

        .tree-row-level-4.tree-row-room-service {
            background-color: #ec8bb9 !important; /* Deeper pink */
            font-size: 10px;
            font-weight: 400;
            color: #1a1a1a !important; /* Darker text for better contrast */
        }

        .tree-row-level-5.tree-row-room-service {
            background-color: #e361a1 !important; /* Deep pink */
            font-size: 10px;
            font-weight: 400;
            color: #ffffff !important; /* White text for better contrast on darker background */
        }

        /* Fallback colors for rows without specific type class */
        .tree-row-level-0 {
            background-color: #ffffff !important;
            font-size: 14px;
            font-weight: 600;
        }

        .tree-row-level-1 {
            background-color: #f8f9fa !important;
            font-size: 13px;
            font-weight: 500;
        }

        .tree-row-level-2 {
            background-color: #e9ecef !important;
            font-size: 12px;
            font-weight: 400;
        }

        .tree-row-level-3 {
            background-color: #dee2e6 !important;
            font-size: 11px;
            font-weight: 400;
        }

        .tree-row-level-4 {
            background-color: #ced4da !important;
            font-size: 10px;
            font-weight: 400;
        }

        .tree-row-level-5 {
            background-color: #adb5bd !important;
            font-size: 10px;
            font-weight: 400;
        }

        /* Expand/Collapse Toggle - Compact with level-specific indentation */
        .tree-toggle {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 16px; /* Reduced from 20px */
            height: 16px; /* Reduced from 20px */
            margin-right: 4px; /* Reduced from 6px */
            cursor: pointer;
            border-radius: 2px;
            transition: all 0.2s ease;
            flex-shrink: 0;
            font-size: 10px; /* Smaller caret */
        }

        .tree-toggle:hover {
            background-color: rgba(0,0,0,0.1);
        }

        .tree-toggle.expanded {
            transform: rotate(90deg);
        }

        .tree-toggle.collapsed {
            transform: rotate(0deg);
        }

        .tree-toggle-placeholder {
            display: inline-block;
            width: 16px; /* Reduced from 20px */
            margin-right: 4px; /* Reduced from 6px */
            flex-shrink: 0;
        }

        /* Level-specific caret indentation - Optimized */
        .tree-toggle-level-0 {
            margin-left: 0px !important;
        }

        .tree-toggle-level-1 {
            margin-left: 1px !important;
        }

        .tree-toggle-level-2 {
            margin-left: 2px !important;
        }

        .tree-toggle-level-3 {
            margin-left: 3px !important;
        }

        .tree-toggle-level-4 {
            margin-left: 4px !important;
        }

        .tree-toggle-level-5 {
            margin-left: 5px !important;
        }

        /* Tree Icons - Ultra Compact */
        .tree-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 12px; /* Further reduced */
            height: 12px; /* Further reduced */
            margin-right: 4px; /* Further reduced */
            font-size: 10px; /* Further reduced */
            flex-shrink: 0;
        }

        .tree-icon.folder {
            color: #ffc107;
        }

        .tree-icon.file {
            color: #6c757d;
        }

        /* Tree Text - Level-specific styling */
        .tree-text {
            flex: 1;
            color: #333;
            line-height: 1.2; /* Compact line height */
        }

        /* Type Badge Styling - Compact */
        .type-badge {
            display: inline-block;
            padding: 2px 6px; /* Reduced padding */
            border-radius: 8px; /* Smaller radius */
            font-size: 8px; /* Smaller font */
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px; /* Reduced letter spacing */
        }

        .type-badge.group {
            background-color: #0ea5e9;
            color: #ffffff;
            border: 1px solid #0284c7;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .type-badge.room-service {
            background-color: #d946ef;
            color: #ffffff;
            border: 1px solid #c026d3;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        /* DataTable Row Styling - Ultra Compact */
        #table-data tbody tr {
            transition: background-color 0.2s ease;
            height: 28px; /* Ultra compact row height */
        }

        #table-data tbody td {
            padding: 4px 8px; /* Reduced padding */
            vertical-align: middle;
        }

        /* Enhanced hover effects for different types */
        #table-data tbody tr.tree-row-group:hover {
            filter: brightness(0.9) saturate(1.1); /* Slightly darker and more saturated for groups */
        }

        #table-data tbody tr.tree-row-room-service:hover {
            filter: brightness(0.9) saturate(1.1); /* Slightly darker and more saturated for files */
        }

        #table-data tbody tr:hover {
            filter: brightness(0.95); /* Fallback hover effect */
        }

        #table-data tbody tr.tree-row-group {
            /* Background handled by level-specific classes */
        }

        #table-data tbody tr.tree-row-room-service {
            /* Background handled by level-specific classes */
        }

        #table-data tbody tr.tree-row-collapsed {
            display: none;
        }

        /* Compact table cell padding */
        #table-data tbody td {
            padding: 4px 8px !important; /* Reduced padding */
            vertical-align: middle;
        }

        /* Removed duplicate level-specific font sizes - handled above */

        /* Tree name styling for better readability */
        .tree-name {
            display: inline-block;
            margin-left: 4px;
            line-height: 1.3;
        }

        /* Responsive adjustments - Extra compact on mobile */
        @media (max-width: 768px) {
            .tree-indent-level-1 { margin-left: 12px; }
            .tree-indent-level-2 { margin-left: 24px; }
            .tree-indent-level-3 { margin-left: 36px; }
            .tree-indent-level-4 { margin-left: 48px; }
            .tree-indent-level-5 { margin-left: 60px; }

            .tree-toggle {
                width: 14px;
                height: 14px;
                font-size: 9px;
            }

            .tree-icon {
                width: 12px;
                height: 12px;
                font-size: 10px;
            }

            #table-data tbody tr {
                height: 28px; /* Even more compact on mobile */
            }

            .tree-row-level-0 { font-size: 12px; }
            .tree-row-level-1 { font-size: 11px; }
            .tree-row-level-2 { font-size: 10px; }
            .tree-row-level-3 { font-size: 9px; }
            .tree-row-level-4 { font-size: 9px; }
            .tree-row-level-5 { font-size: 8px; }
        }

        /* Loading state */
        .tree-loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .tree-loading .tree-toggle {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
@endpush

@push('menu')
    @include('menu.master_data')
@endpush

@section('content')
    <div class="datatable-modern-container">
        <div class="datatable-control-bar">
            <div class="datatable-search-container">
                <div class="datatable-search-input">
                    <input type="text" id="searchInput" placeholder="Search in room table">
                </div>
            </div>
            <div class="datatable-action-buttons">
                <button type="button" class="btn btn-primary btn-modern-rounded add-btn" data-bs-toggle="modal"
                        data-bs-target="#modal-form">
                    <i class="fas fa-plus"></i> Tambah Data
                </button>
                <button type="button" class="btn btn-success btn-modern-rounded import-btn" data-bs-toggle="modal"
                        data-bs-target="#modal-import-excel">
                    <i class="fas fa-file-excel"></i> Import Excel
                </button>
                <div class="datatable-filter-icon btn-modern-rounded" data-bs-toggle="modal"
                     data-bs-target="#filterDrawer">
                    <i class="fas fa-filter"></i>
                </div>
            </div>
        </div>

        <div class="datatable-table-container">
            <div class="table-responsive">
                <table class="table table-sm w-100" id="table-data">
                    <thead>
                    <tr>
                        <th>No</th>
                        <th>Nama</th>
                        <th>Kode</th>
                        <th>Parent</th>
                        <th>Tipe</th>
                        <th>Action</th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>

    </div>

    <!-- Modal Form -->
    <div class="modal fade" id="modal-form">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Form Aspak Room</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('master-data.aspak-room.store') }}" method="post" id="formdata">
                        <div class="form-group mb-3">
                            <label class="form-label">Kode Room Service</label>
                            <input type="text" class="form-control" name="room_service_code" id="room_service_code">
                            <div class="d-block text-danger error-message" id="error_room_service_code"></div>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label">Nama Room Service</label>
                            <input type="text" class="form-control" name="room_service_name" id="room_service_name">
                            <div class="d-block text-danger error-message" id="error_room_service_name"></div>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label">Tipe</label>
                            <select class="form-control" name="type" id="type" style="width: 100%;">
                                <option value="">Pilih Tipe</option>
                                <option value="GROUP">GROUP (Folder)</option>
                                <option value="ROOM_SERVICE">ROOM_SERVICE (File)</option>
                            </select>
                            <div class="d-block text-danger error-message" id="error_type"></div>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label">Parent (Opsional)</label>
                            <select class="form-control" name="parent_id" id="parent_id" style="width: 100%;">
                                <option value="">Pilih Parent (Kosong untuk Root)</option>
                            </select>
                            <div class="d-block text-danger error-message" id="error_parent_id"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Batal</a>
                    <a href="javascript:;" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i>Simpan</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Import Excel -->
    <div class="modal fade" id="modal-import-excel">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Import Excel Aspak Room</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <!-- Area untuk menampilkan pesan sukses/error -->
                    <div id="import-message-area" class="d-none mb-3">
                        <div id="import-success-message" class="alert alert-success d-none">
                            <i class="fas fa-check-circle me-1"></i>
                            <span id="success-text"></span>
                        </div>
                        <div id="import-error-message" class="alert alert-danger d-none">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <span id="error-text"></span>
                            <div id="error-details" class="mt-3"></div>
                        </div>
                    </div>

                    <form id="form-import-excel" enctype="multipart/form-data">
                        <div class="form-group mb-3">
                            <label class="form-label">File Excel</label>
                            <input type="file" class="form-control" name="excel_file" id="excel_file"
                                   accept=".xlsx,.xls" required>
                            <div class="form-text">Format file yang didukung: .xlsx, .xls</div>
                            <div class="d-block text-danger error-message" id="error_excel_file"></div>
                        </div>
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-1"></i>Format Excel yang diperlukan:</h6>
                            <ul class="mb-0">
                                <li>Kolom A: Kode Room Service</li>
                                <li>Kolom B: Nama Room Service</li>
                                <li>Kolom C: Tipe (GROUP/ROOM_SERVICE)</li>
                                <li>Kolom D: Parent ID (Opsional)</li>
                            </ul>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal"
                       id="btn-cancel-import">Batal</a>
                    <a href="javascript:;" class="btn btn-success" id="btn-import-submit">
                        <i class="fas fa-upload me-1"></i>Import
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/master/app.js') }}"></script>

    <script src="{{ asset('js/app/master/aspak-room.js') }}"></script>
@endpush
